import { getLocalStorageItem } from '../../globals/utils'
import { filterByTimeRange } from '../NotificationCenter/utils'

const NOTIFICATION_KEYS = {
  LATEST: 'latest-notifications',
  PREVIOUS: 'previous-notifications',
}

// Helper to get current timestamp
export const getCurrentTimestamp = () => new Date().getTime()

export const fetchNotificationsFromStorage = (type: 'latest' | 'previous') => {
  // fetch notifications from local storage based on given props
  const key =
    type === 'latest' ? NOTIFICATION_KEYS.LATEST : NOTIFICATION_KEYS.PREVIOUS

  // get notification keep duration from local storage
  const notificationKeepDuration =
    getLocalStorageItem('preferences')?.clearAfter

  const storedNotifications: any = localStorage.getItem(key)

  if (storedNotifications === null) return []

  let notifications = []

  try {
    if (typeof storedNotifications === 'string')
      notifications = storedNotifications
        ? JSON.parse(storedNotifications || '[]')
        : []
    else notifications = storedNotifications
  } catch (error) {
    console.error('Error parsing notifications:', error)
  }

  if (type === 'latest') return notifications

  if (type === 'previous')
    // filter previous notifications based on keep duration
    return filterByTimeRange(notifications, notificationKeepDuration)
}
export const saveNotificationsToStorage = (
  type: 'latest' | 'previous',
  notifications: NotificationFromFirebase[]
) => {
  // save notifications to local storage based on given props
  const key =
    type === 'latest' ? NOTIFICATION_KEYS.LATEST : NOTIFICATION_KEYS.PREVIOUS
  localStorage.setItem(key, JSON.stringify(notifications))
}

export interface NotificationFromFirebase {
  title: string
  type: 'page' | 'browser' | 'both'
  body: string
  url: string
  timeStamp: string // ISO format timestamp
  duration: string // Duration in seconds (could also be number if preferred)
  icon: string
  id: string
  domain: string
  ctaText: string
  category: string
  isSystemNotification: 'true' | 'false'
  ctaTarget: '_self' | '_blank'
  isDismissable: string
}
export const CheckIsNotiCategoryAllowed = ({
  payloadCategory,
}: {
  payloadCategory: string[]
}) => {
  // check if user preferences category is allowed
  const notificationCategory =
    getLocalStorageItem('preferences')?.allowedNotificationTypes

  const isCategoryAllowed =
    Array.isArray(payloadCategory) &&
    payloadCategory?.some((category) =>
      notificationCategory?.includes(category)
    )

  return isCategoryAllowed
}

export const getCountryName = (code: string, locale = 'en'): string => {
  if (!code) return 'Unknown Country'
  try {
    const regionNames = new Intl.DisplayNames([locale], { type: 'region' })
    return regionNames.of(code.toUpperCase()) || 'Unknown Country'
  } catch {
    return 'Unknown Country'
  }
}
