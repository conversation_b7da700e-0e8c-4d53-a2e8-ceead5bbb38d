import { NextFetchEvent, NextRequest, NextResponse } from 'next/server'
import { BUISNESS_DOMAINS, getDomainShortName } from './globals/utils'
import { EXPERIMENT_TYPE } from './lib/constant'
import {
  CookieSetTopagesforExperimentation,
  getConfigurationByScopeAndType,
  getUserIDforExperiment,
  handelExperimentStatsigViaRedirection,
  handelExperimentbasedonVariantParam,
  withSlashes,
} from './lib/experimentation'

let Paths: Record<string, PathData> = {}

// Languages supported for experimentation Domain Specific
// currently only for Altus and Reonomy as languages setup is not done for other domains like handeling forward language /en/slug
const languages = {
  domainAltusGroupCom: ['', 'fr'],
  domainReonomyCom: [''],
}
const ALLOWEDED_DOMAINS = ['domainAltusGroupCom', 'domainReonomyCom']
const BLOCKED_PREFIX = 'i/'

export default async function middleware(
  request: NextRequest,
  event: NextFetchEvent
) {
  const { pathname } = request.nextUrl
  const { userId, hasUserId } = getUserIDforExperiment(request)
  const isPreview = process.env.NEXT_PUBLIC_PREVIEW === 'true'

  // Redirect if the path starts with a blocked prefix
  if (pathname?.replace(/^\/+/, '')?.startsWith(BLOCKED_PREFIX)) {
    const url = request.nextUrl.clone()
    url.pathname = '/not-found'
    return NextResponse.redirect(url)
  }

  // empty Paths object to store experimentation data
  Paths = {}

  //allowded only domains
  if (
    process.env.NEXT_PUBLIC_DOMAIN &&
    ALLOWEDED_DOMAINS.includes(process.env.NEXT_PUBLIC_DOMAIN)
  ) {
    const dataRaw = await getConfigurationByScopeAndType(
      'Experimentation',
      `${getDomainShortName(process.env.NEXT_PUBLIC_DOMAIN!)?.toUpperCase()}`
    )
    const data = dataRaw as ExperimentationPageData

    // data iteration to populate Paths
    data?.data?.forEach((expPage) => {
      if (
        expPage?.masterPage &&
        expPage?.experimentationId &&
        !expPage?.isDeleted &&
        (isPreview || expPage?.isPublished)
      ) {
        const basePath = withSlashes(expPage?.masterPage)
        languages[
          process.env.NEXT_PUBLIC_DOMAIN! as keyof typeof languages
        ]?.forEach((lang) => {
          const langPath = lang ? `${lang}/` : ''
          Paths[`${basePath}${langPath}`] = {
            experimentationId: expPage.experimentationId,
            historyCount: expPage?.historyCount ?? 0,
            experimentationType:
              expPage?.experimentationType ?? EXPERIMENT_TYPE.STATSIG,
            experimentationPages:
              expPage?.experimentationPages?.map((page) =>
                page?.slug ? `/${page?.slug}/${langPath}` : `/${langPath}`
              ) ?? [],
          }
        })
      }
    })
  }

  const pathData = Paths?.[pathname]

  const origin = request.headers.get('origin') ?? ''
  let lang = request.nextUrl.searchParams.get('lang') ?? ''
  let experimentVariant = request.nextUrl.searchParams.get('variant')

  // Non Experimental Routes
  if (!pathData?.experimentationId) {
    if (lang && BUISNESS_DOMAINS['altus'] == process.env.NEXT_PUBLIC_DOMAIN) {
      const rewriteUrl = new URL(
        `${encodeURI(request.nextUrl.pathname)}${lang}${request.nextUrl.search}`,
        request.url
      )
      return NextResponse.rewrite(rewriteUrl)
    }
    const response = NextResponse.next()
    CookieSetTopagesforExperimentation({
      req: request,
      response,
      hasUserId,
      userId,
      origin,
    })

    return response
  }
  const { experimentationId, experimentationType } = pathData
  // experimente has ?variant={anything} in the url
  if (
    experimentationId &&
    experimentVariant &&
    Paths?.[pathname]?.experimentationPages?.[
      Number(experimentVariant) - (Paths?.[pathname]?.historyCount ?? 0)
    ]
  ) {
    if (experimentationType === EXPERIMENT_TYPE.STATSIG) {
      return await handelExperimentbasedonVariantParam({
        req: request,
        userId,
        hasUserId,
        lang,
        variantPage:
          Paths?.[pathname]?.experimentationPages?.[
            Number(experimentVariant) - (Paths?.[pathname]?.historyCount ?? 0)
          ],
      })
    }
  } else if (
    experimentationId &&
    experimentationType === EXPERIMENT_TYPE.STATSIG
  ) {
    // If the path is for Statsig experimentation, handle it via redirection
    return await handelExperimentStatsigViaRedirection({
      req: request,
      event,
      userId,
      hasUserId,
      Paths,
    })
  }

  // if the path is not for experimentation, rewrite the URL as normal and setup cookies for future experimentation
  const response = NextResponse.next()
  CookieSetTopagesforExperimentation({
    req: request,
    response,
    hasUserId,
    userId,
    origin,
  })

  return response
}

// Make this middleware work for every request
export const config = {
  matcher:
    '/((?!_next/static|static|_next/image|favicon|android-chrome-192x192.png|android-chrome-512x512.png).*)',
}
