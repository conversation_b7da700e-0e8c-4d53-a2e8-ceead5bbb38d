import { createSlice } from '@reduxjs/toolkit'
import { PayloadAction } from '@reduxjs/toolkit/dist/createAction'
import { useMemo } from 'react'

import { AppConfigD } from '../../globals/defaults'
import { RootState, useAppSelector } from '../store'

interface AppSliceState {
  isLightMode: boolean
  isBlur: boolean
  isTranslucent: boolean
  geoLocationData: {
    countryCode?: string
    countryName?: string
    businessRegion?: string
    currentPageRegion?: string
  }
}

// define initial state here
const initialState: AppSliceState = {
  ...AppConfigD.app,
  isBlur: false,
  isTranslucent: false,
  geoLocationData: {}
}

const appSlice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    setAppTheme: (state, action: PayloadAction<boolean>) => {
      state.isLightMode = action.payload
    },
    setIsBlur: (state, action: PayloadAction<boolean>) => {
      state.isBlur = action.payload
    },
    setIsTranslucent: (state, action: PayloadAction<boolean>) => {
      state.isTranslucent = action.payload
    },
    setGeoLocationData: (state, action: PayloadAction<any>) => {
      state.geoLocationData = action.payload
    }
  },
})

//export actions like this..
export const { setAppTheme, setIsBlur, setIsTranslucent, setGeoLocationData } = appSlice.actions

//define individual selectors for each reducer state
export const selectAppTheme = (state: RootState) => state.app.isLightMode

export function useAppTheme() {
  const appTheme = useAppSelector(selectAppTheme)
  return useMemo(() => appTheme, [appTheme])
}

export default appSlice.reducer
