'use client'
import { use<PERSON>allback, useEffect, useState } from 'react'
import LayoutContainer from '../../../components/Containers/LayoutContainer'
import Richtext from '../../../components/ContentBlocks/Richtext'
import Kernel from '../../../components/Kernel'
import { BackgroundImage } from '../../../components/Multimedia/Images'
import ErrorToast from '../../../components/Notifications/Toasts/ErrorToast'
import SpinnerToast from '../../../components/Notifications/Toasts/Spinner Toast'
import SuccessToast from '../../../components/Notifications/Toasts/SuccessToast'
import {
  getLocalStorageItem,
  getSessionStorageItem,
  setLocalStorageItem,
} from '../../../globals/utils'
import { setIsFormActivated } from '../../../redux/slices/formSlice'
import { useAppDispatch, useAppSelector } from '../../../redux/store'
import FormInputType from '../Form/inputType'
import { formStartTracking, processFormSubmission } from '../Form/utils'
import { FormFloatingInsightsD } from './defaults'
import styles from './index.module.scss'
import { FormFloatingInsightsI } from './interface'

export default function FormFloatingInsights(props: FormFloatingInsightsI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */

  const isSubscribed = getLocalStorageItem('altusNewsLetterSubscribed')

  // if newsletter subscription status is true, returning null so that the form doesn't render
  if (isSubscribed) {
    return null
  }

  const updatedProps: FormFloatingInsightsI = {
    ...FormFloatingInsightsD,
    ...props,
  }

  const colorClass = updatedProps?.data?.isLightMode ? 'cp1' : 'cs2'
  const [removeForm, setRemoveForm] = useState(false)
  const [formData, setFormData] = useState(updatedProps?.data)
  const [values, setValues] = useState({})
  const [validation, setValidation] = useState(false)
  const [emailValidationMsg, setEmailValidationMsg] = useState<string | null>(
    null
  )
  const contentfulFormId = updatedProps.data?.sys?.id
  const isFormActivated = useAppSelector((state) => state.form.isFormActivated)
  const dispatch = useAppDispatch()

  const [formSubmitionState, setFormSubmitionState] = useState<
    'NOT SUBMITTED' | 'LOADING' | 'SUCCESS' | 'FAILED' | 'THANK YOU'
  >('NOT SUBMITTED')

  // function to handle form input
  const mainHandleChange = (event: string, name: string) => {
    if (!isFormActivated[contentfulFormId]) {
      formStartTracking(contentfulFormId)
      dispatch(setIsFormActivated({ contentfulFormId }))
    }

    if (event && name) {
      setValues((prev) => ({
        ...prev,
        [name]: event,
      }))
    }
  }

  useEffect(() => {
    if (formSubmitionState === 'SUCCESS') {
      // set newsletter subscription status
      setLocalStorageItem('altusNewsLetterSubscribed', true)
    }
  }, [formSubmitionState])

  // function to handle form submission
  const handleSubmitMain = useCallback(
    (values) => {
      processFormSubmission({
        formData,
        values,
        setFormSubmitionState,
        contentfulFormId,
        hiddenFields: updatedProps.hiddenFields,
      })
    },
    [updatedProps]
  )

  const toasts = (
    <div className={styles.toasts}>
      {' '}
      {formSubmitionState === 'SUCCESS' && (
        <SuccessToast
          setToastState={() => { }}
          simpleParagraph={{
            data: updatedProps?.data?.successMessage?.content,
          }}
          htmlAttr={{
            style: { width: '100%', maxWidth: '900px' },
          }}
        />
      )}
      {formSubmitionState === 'LOADING' && (
        <SpinnerToast
          setToastState={() => setFormSubmitionState('NOT SUBMITTED')}
          simpleParagraph={{
            data: updatedProps?.data?.loadingMessage?.content,
          }}
          htmlAttr={{
            style: { width: '100%', maxWidth: '900px' },
          }}
        />
      )}
      {formSubmitionState === 'FAILED' && (
        <ErrorToast
          setToastState={(val) => {
            !val && setFormSubmitionState('NOT SUBMITTED')
          }}
          simpleParagraph={{
            data: updatedProps?.data?.errorMessage?.content,
          }}
          onClose={() => setFormSubmitionState('NOT SUBMITTED')}
          htmlAttr={{
            style: { width: '100%', maxWidth: '900px' },
          }}
        />
      )}
    </div>
  )

  const BgImage = updatedProps?.data?.backgroundImage ? (
    <>
      <BackgroundImage
        sizes={'100%'}
        isFullXBleed
        zIndex={1}
        src={updatedProps?.data?.backgroundImage?.imageFile?.url}
      />
      <div className={'imageOverlay bo1n1d'}></div>
    </>
  ) : (
    <></>
  )

  useEffect(() => {
    const flag = getSessionStorageItem('isFormClosed')
    if (flag === 'true') {
      setRemoveForm(true)
    } else {
      setRemoveForm(false)
    }
  }, [])

  return removeForm || formSubmitionState === 'THANK YOU' ? (
    <></>
  ) : (
    <Kernel
      {...updatedProps}
      isFullXBleed
      htmlAttr={{
        className: `${styles.formFloatingRoot} ${updatedProps?.data?.htmlAttr?.className}`,
      }}
    >
      {BgImage}
      <LayoutContainer htmlAttr={{ style: { height: '100%' } }}>
        <div className={styles.blurContainer}>
          <div className={styles.formFloatingContainer}>
            {formSubmitionState === 'THANK YOU' ? (
              <Richtext data={updatedProps?.data?.thankYouMessage} />
            ) : (
              <form
                onSubmit={(e) => {
                  e.preventDefault()
                  handleSubmitMain(values)
                }}
                className={styles.fromCon}
              >
                <div className={styles.contentContainer}>
                  {updatedProps?.data?.header?.content && (
                    <div className={styles.leftSection}>
                      <Richtext
                        htmlAttr={{ className: `${colorClass}` }}
                        isLightMode={updatedProps?.data?.isLightMode}
                        data={updatedProps?.data?.header?.content}
                      />
                    </div>
                  )}
                  {formSubmitionState !== 'NOT SUBMITTED' ? (
                    toasts
                  ) : (
                    <div className={styles.formFloatingInputDiv}>
                      {formSubmitionState === 'NOT SUBMITTED' &&
                        formData &&
                        formData?.formFieldsCollection &&
                        formData?.formFieldsCollection?.items?.map(
                          (input, index: number) => (
                            <FormInputType
                              key={index}
                              input={input}
                              contentfulFormId={contentfulFormId}
                              formSubmitionState={formSubmitionState}
                              toasts={toasts}
                              formTemplate={formData?.template}
                              setValidation={setValidation}
                              validation={validation}
                              mainHandleChange={mainHandleChange}
                              values={values}
                              isLightMode={updatedProps?.data?.isLightMode}
                              setEmailValidationMsg={setEmailValidationMsg}
                              emailValidationMsg={emailValidationMsg}
                              isDynamicAgreement={formData?.isDynamicAgreement}
                            />
                          )
                        )}
                    </div>
                  )}
                </div>
                <input
                  type='hidden'
                  name='formid'
                  value={contentfulFormId}
                  className={'d-none'}
                />
              </form>
            )}
          </div>
        </div>
      </LayoutContainer>
    </Kernel>
  )
}
