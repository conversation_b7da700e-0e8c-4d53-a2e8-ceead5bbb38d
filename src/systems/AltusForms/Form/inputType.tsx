import { useId } from 'react'
import SimpleButtonWIcon from '../../../components/CTAs/SimpleButtonWIcon'
import FlexContainer from '../../../components/Containers/FlexContainer'
import LayoutContainer from '../../../components/Containers/LayoutContainer'
import Richtext from '../../../components/ContentBlocks/Richtext'
import LayoutRouter from '../../../lib/componentsRouter/LayoutRouter'
import { MCA_LINKS } from '../../../lib/constant'
import {
  assignFormValues,
  changeFormValueMain,
} from '../../../redux/slices/formSlice'
import { useAppDispatch, useAppSelector } from '../../../redux/store'
import { lowerCaseFirstLetter } from '../../AFS/AFSPages/utils'
import floatingHomeStyle from '../FormFloatingHome/index.module.scss'
import EmailField from './@core/EmailField'
import { countries, reonomyFreeTrialCountries } from './country'
import styles from './index.module.scss'
import { deleteDependantFields, getAgreementLinkByCountry, getDependantFieldNames } from './utils'

/**
 * React functional component for rendering form input fields based on their types.
 */

const FormInputType = (props: FormInputTypeProps) => {
  const {
    input,
    mainHandleChange,
    values,
    setValidation,
    validation,
    formTemplate,
    formSubmitionState,
    toasts,
    contentfulFormId,
    setEmailValidationMsg,
    emailValidationMsg,
    isLightMode = true,
    isDynamicAgreement
  } = props
  // Return null if input is not provided.
  const dispatch = useAppDispatch()
  const geoLocationData = useAppSelector(
    (state) => state.app.geoLocationData
  )

  if (!input) return null
  // Check if the input type is a 'LinkComponent'.
  if (
    input?.hasOwnProperty('__typename') &&
    input?.__typename === 'LinkComponent'
  ) {
    let btnClass = `${styles.btn}`
    if (
      formTemplate === 'Floating' ||
      formTemplate === 'Floating - Home' ||
      formTemplate === 'FloatingComponent'
    ) {
      btnClass = `${floatingHomeStyle.btn} `
    }
    // Return a submit button component with associated properties.

    return formSubmitionState !== 'NOT SUBMITTED' ? (
      toasts
    ) : (
      <SimpleButtonWIcon
        isButton
        isFormButton
        htmlAttr={{
          ...input?.htmlAttr,
          className: input?.htmlAttr?.className
            ? input?.htmlAttr?.className
            : btnClass,
          type: 'submit',
          onClick: () => {
            setValidation(true)
          },
        }}
        isIconPrefixed={input?.iconPlacement === 'Prefix'}
        isChevron2Arrow={input?.isChevron2Arrow}
        textContent={input?.text ?? 'Submit'}
        icon='RightArrow'
        variant='primary'
        isLightMode={isLightMode}
      />
    )
  }

  // Check if the input type is a 'ComponentLayoutRow'.
  if (
    input?.hasOwnProperty('__typename') &&
    input?.__typename === 'ComponentLayoutRow'
  ) {
    // Create updatedProps and render a LayoutRouter component.
    const updatedProps = {
      ...input,
      setEmailValidationMsg,
      emailValidationMsg,
      onChange: mainHandleChange,
      values: values,
      contentfulFormId,
      validation,
    }
    const isFormBgLayout = updatedProps?.htmlAttr?.className
      ?.split(' ')
      .includes('form-bg-layout')
    if (isFormBgLayout) {
      return (
        <LayoutContainer {...updatedProps}>
          {updatedProps?.layoutColumnCollection?.items.map((colItem) => {
            return colItem?.layoutItemCollection?.items.map(
              (lyoutItem, index: number) => {
                let obj =
                  lyoutItem?.[lowerCaseFirstLetter(lyoutItem.__typename)] ?? {}

                if (lyoutItem?.__typename === 'ComponentLayoutRow') {
                  return (
                    <LayoutRouter
                      {...obj}
                      htmlAttr={{
                        ...obj?.htmlAttr,
                        className: `${styles.layout} ${obj?.htmlAttr?.className}`,
                      }}
                    />
                  )
                } else if (lyoutItem?.__typename === 'LinkComponent') {
                  return formSubmitionState !== 'NOT SUBMITTED' ? (
                    toasts
                  ) : (
                    <SimpleButtonWIcon
                      isButton
                      isFormButton
                      htmlAttr={{
                        ...obj?.htmlAttr,
                        className: `${obj?.htmlAttr?.className ? obj?.htmlAttr?.className : styles.btn}`,
                        type: 'submit',
                        onClick: () => {
                          setValidation(true)
                        },
                      }}
                      isIconPrefixed={obj?.iconPlacement === 'Prefix'}
                      isChevron2Arrow={obj?.isChevron2Arrow}
                      textContent={obj?.text ?? 'Submit'}
                      icon='RightArrow'
                      variant='primary'
                    />
                  )
                } else {
                  return (
                    <FormInputType
                      key={index}
                      {...props}
                      input={
                        lyoutItem?.[lowerCaseFirstLetter(lyoutItem?.__typename)]
                      }
                    />
                  )
                }
              }
            )
          })}
        </LayoutContainer>
      )
    } else {
      return (
        <LayoutRouter
          {...updatedProps}
          htmlAttr={{
            ...updatedProps?.htmlAttr,
            className: `${styles.layout} ${updatedProps?.htmlAttr?.className}`,
          }}
        />
      )
    }
  }

  // Check if the input has a 'sectionItemsCollection' with items.
  if (
    input?.hasOwnProperty('sectionItemsCollection') &&
    input?.sectionItemsCollection?.hasOwnProperty('items') &&
    input?.sectionItemsCollection?.items?.length > 0
  ) {
    // Recursively render each item in the sectionItemsCollection.
    return (
      <>
        {input?.sectionItemsCollection?.items?.map((input, index: number) => (
          <FormInputType
            key={index}
            {...props}
            input={input?.[lowerCaseFirstLetter(input?.__typename)]}
          />
        ))}
      </>
    )
  }
  // Check if the input type is 'Text', 'Date', or 'Hidden'.
  if (input?.altusFieldType === 'Text') {
    const randomId = useId()
    // Return a text input component with associated properties.
    return (
      <div
        style={{
          display: input?.altusFieldType === 'Hidden' ? 'none' : 'block',
          width: '100%',
          marginBottom: '30px',
        }}
      >
        <label htmlFor={randomId} className='form-label'>
          {input?.altusLabel}
        </label>
        <input
          type={input?.altusDataFormat === 'Phone' ? 'number' : 'text'}
          name={input?.altusFieldName}
          className='form-control'
          id={randomId}
          // pattern={generateEmailValidatioMsg(
          //   notAllowedEmails,
          //   '',
          //   input?.isAltusEmailAllowed,
          //   input?.isGenericEmailAllowed
          // )}
          required={input?.altusIsRequired}
          placeholder={input?.placeholderText}
          // defaultValue={values?.[input?.altusFieldName] ?? ''}
          onChange={(event) =>
            mainHandleChange(event.target.value, input?.altusFieldName)
          }
        />
        {input?.helperText && (
          <small className='form-text'>{input?.helperText}</small>
        )}
        {input?.validationErrorMessage && (
          <div className='invalid-feedback'>
            {input?.validationErrorMessage ??
              input?.altusLabel + ' is a required field'}
          </div>
        )}
      </div>
    )
  }

  if (input?.altusFieldType === 'Hidden') {
    return (
      <input
        type='hidden'
        name={input?.altusFieldName}
        value={input?.altusFieldValues?.at(0) ?? ''}
      />
    )
  }

  if (input?.altusFieldType === 'Date') {
    const randomId = useId()
    return (
      <div
        style={{
          width: '100%',
          marginBottom: '30px',
        }}
      >
        <label htmlFor={randomId} className='form-label'>
          {input?.altusLabel}
        </label>
        <input
          type='date'
          name={input?.altusFieldName}
          className='form-control'
          id={randomId}
          placeholder={input?.placeholderText}
          // defaultValue={values?.[input?.altusFieldName]}
          onChange={(event) =>
            mainHandleChange(event.target.value, input?.altusFieldName)
          }
        />
        {input?.helperText && (
          <small className='form-text'>{input?.helperText}</small>
        )}
        {input?.validationErrorMessage && (
          <div className='invalid-feedback'>
            {input?.validationErrorMessage ??
              input?.altusLabel + ' is a required field'}
          </div>
        )}
      </div>
    )
  }

  if (input?.altusFieldType === 'Password') {
    const randomId = useId()
    // Return an password input component with associated properties.
    return (
      <div
        style={{
          width: '100%',
          marginBottom: '30px',
        }}
      >
        <label htmlFor={randomId} className='form-label'>
          {input?.altusLabel}
        </label>
        <input
          type='password'
          name={input?.altusFieldName}
          className='form-control'
          id={randomId}
          minLength={8}
          required={input?.altusIsRequired}
          placeholder={input?.placeholderText}
          onChange={(event) =>
            mainHandleChange(event.target.value, input?.altusFieldName)
          }
        />
        {input?.helperText && (
          <small className='form-text'>{input?.helperText}</small>
        )}
        {input?.validationErrorMessage && (
          <div className='invalid-feedback'>
            {input?.validationErrorMessage}
          </div>
        )}
      </div>
    )
  }

  // Check if the input type is 'Email'.
  if (input?.altusFieldType === 'Email') {
    return <EmailField {...props} />
  }

  // Check if the input type is 'Text Area'.
  if (input?.altusFieldType === 'Text Area') {
    const randomId = useId()
    // Return a textarea component with associated properties.
    return (
      <div
        style={{
          width: '100%',
          marginBottom: '30px',
        }}
      >
        <label htmlFor={randomId} className='form-label'>
          {input?.altusLabel}
        </label>
        <textarea
          name={input?.altusFieldName}
          className='form-control'
          id={randomId}
          required={input?.altusIsRequired}
          onChange={(event) =>
            mainHandleChange(event.target.value, input?.altusFieldName)
          }
          placeholder={input?.placeholderText}
          // defaultValue={values?.[input?.altusFieldName] ?? ''}
          rows={4}
        ></textarea>
        {input?.helperText && (
          <small className='form-text'>{input?.helperText}</small>
        )}
        {input?.validationErrorMessage && (
          <div className='invalid-feedback'>
            {input?.validationErrorMessage}
          </div>
        )}
      </div>
    )
  }

  // Check if the input type is 'Radio'.
  if (input?.altusFieldType === 'Radio') {
    // Return a radio input component with associated properties.
    return (
      <div style={{ width: '100%', marginBottom: '20px' }}>
        <label htmlFor={input?.altusFieldName} className='form-label'>
          {input?.altusLabel}
        </label>
        <div className={styles.radioDiv}>
          {input?.altusFieldValues?.map((item: string) => {
            const randomId = useId()
            return (
              <div key={item}>
                <input
                  type='radio'
                  id={randomId}
                  name='radioGroup'
                  required={input?.altusIsRequired}
                  defaultChecked={values?.[input?.altusFieldName] === 'Yes'}
                  onChange={(event) =>
                    mainHandleChange(event.target.value, input?.altusFieldName)
                  }
                />
                <label htmlFor={randomId}>{item}</label>
              </div>
            )
          })}
        </div>
      </div>
    )
  }

  // Check if the input type is 'CheckBox'.
  if (input?.altusFieldType === 'CheckBox') {
    const randomId = useId()
    // Return a checkbox input component with associated properties.
    return (
      <div style={{ marginBottom: '30px' }}>
        <FlexContainer
          key={input?.altusFieldName}
          htmlAttr={{ className: styles.checkboxDiv }}
          width='100%'
          minWidth='190px'
          gap='8px'
          alignItems='center'
        >
          <input
            type='checkbox'
            id={randomId}
            name={input?.altusFieldName}
            required={isDynamicAgreement ? true : input?.altusIsRequired}
            defaultChecked={values?.[input?.altusFieldName] === 'Yes'}
            onChange={(event) => {
              mainHandleChange(
                event?.target?.checked
                  ? (input?.altusFieldValues?.at(0) ?? 'Yes')
                  : 'No',
                input?.altusFieldName
              )
            }}
          />
          {isDynamicAgreement ? <label style={{ padding: '0', width: '100%' }} htmlFor={randomId}>
            I accept the <a href={getAgreementLinkByCountry({
              countryCode: geoLocationData?.countryCode,
              businessRegion: geoLocationData?.businessRegion,
            })} target='_blank'>Master Customer Agreement</a>
            {" "}and <a href={MCA_LINKS.FORBURY} target='_blank'>Forbury Addendum</a>.
          </label> :
            <label style={{ padding: '0', width: '100%' }} htmlFor={randomId}>
              {input?.richLabel ? (
                <Richtext data={input?.richLabel} />
              ) : (
                input?.altusLabel
              )}
            </label>
          }
        </FlexContainer>
      </div>
    )
  }

  // Check if the input type is 'Dropdown'.
  if (input?.altusFieldType === 'Dropdown') {
    const dependantNames = getDependantFieldNames(input)
    const randomId = useId()
    // Return a dropdown select component with associated properties.
    return (
      <>
        <div style={{ marginBottom: '30px' }} className={'dropdownRoot'}>
          <label htmlFor={randomId} className='form-label'>
            {input?.altusLabel}
          </label>
          <div className='select'>
            <select
              onChange={(event) => {
                const updatedValues = deleteDependantFields(
                  values,
                  dependantNames
                )
                dispatch(assignFormValues(updatedValues))
                mainHandleChange(event.target.value, input?.altusFieldName)
              }}
              placeholder={input?.placeholderText}
              // defaultValue={values?.[input?.altusFieldName] ?? ''}
              className={'form-control dropdownDiv'}
              name={input?.altusFieldName}
              required={input?.altusIsRequired}
              id={randomId}
            >
              <option value={''}>{input?.placeholderText}</option>
              {input?.altusFieldValues?.map((item, index) => (
                <option value={item} key={item}>
                  {input?.userFieldLabels?.[index] ?? item}
                </option>
              ))}
            </select>
            {input?.validationErrorMessage && (
              <div className='invalid-feedback'>This is a required field</div>
            )}
          </div>
          {input?.helperText && (
            <small className='form-text'>{input?.helperText}</small>
          )}
        </div>
        {/* Render dependent fields if any */}
        {input?.altusDependentsCollection &&
          input?.altusDependentsCollection.items.map(
            (inputItem: any, index: number) => {
              if (
                values?.hasOwnProperty(input?.altusFieldName) &&
                inputItem?.dependentValue === values[input?.altusFieldName] &&
                values[input?.altusFieldName] !== ''
              )
                return inputItem?.dependentFieldsCollection?.items.map(
                  (dependentItem: any) => (
                    <FormInputType
                      key={index}
                      {...props}
                      input={dependentItem}
                    />
                  )
                )
            }
          )}
      </>
    )
  }

  // Check if the input type is 'Country'.
  if (input?.altusFieldType === 'Country') {
    const randomId = useId()
    // Return a country select component with associated properties.
    return (
      <>
        <div style={{ marginBottom: '30px' }} className={'dropdownRoot'}>
          <label htmlFor={randomId} className='form-label'>
            {input?.altusLabel}
          </label>
          <div className='select'>
            <select
              onChange={(event) => {
                dispatch(changeFormValueMain({ state: undefined }))
                mainHandleChange(event.target.value, input?.altusFieldName)
              }}
              required={input?.altusIsRequired}
              placeholder={input?.placeholderText}
              // defaultValue={values?.[input?.altusFieldName] ?? ''}
              className={'form-control dropdownDiv'}
              name={input?.altusFieldName}
              id={randomId}
            >
              <option value={''}>{input?.placeholderText}</option>
              {/* Map through the list of countries and render options */}
              {formTemplate === 'ReonomyFreeTrial'
                ? reonomyFreeTrialCountries?.map(
                  (item: { country: string }) => (
                    <option value={item.country} key={item.country}>
                      {item.country}
                    </option>
                  )
                )
                : countries?.map((item: { country: string }) => (
                  <option value={item.country} key={item.country}>
                    {item.country}
                  </option>
                ))}
            </select>

            {input?.validationErrorMessage && (
              <div className='invalid-feedback'>
                {input?.validationErrorMessage ?? 'This is a required field'}
              </div>
            )}
          </div>
          {input?.helperText && (
            <small className='form-text'>{input?.helperText}</small>
          )}
        </div>
        {input?.altusDependentsCollection &&
          input?.altusDependentsCollection.items.map(
            (inputItem: any, index: number) => {
              if (
                values?.hasOwnProperty(input?.altusFieldName) &&
                inputItem?.dependentValue === values[input?.altusFieldName] &&
                values[input?.altusFieldName] !== ''
              )
                return inputItem?.dependentFieldsCollection?.items.map(
                  (dependentItem: any) => (
                    <FormInputType
                      key={index}
                      {...props}
                      input={dependentItem}
                    />
                  )
                )
            }
          )}
      </>
    )
  }

  // Return an empty fragment for unsupported input types.
  return <></>
}

export default FormInputType
