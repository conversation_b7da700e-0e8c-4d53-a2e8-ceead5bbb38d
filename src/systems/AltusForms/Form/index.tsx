'use client'
import { useCallback, useState } from 'react'
import SimpleContainer from '../../../components/Containers/SimpleContainer'
import Richtext from '../../../components/ContentBlocks/Richtext'
import ErrorToast from '../../../components/Notifications/Toasts/ErrorToast'
import SpinnerToast from '../../../components/Notifications/Toasts/Spinner Toast'
import SuccessToast from '../../../components/Notifications/Toasts/SuccessToast'
import {
  setIsFormActivated
} from '../../../redux/slices/formSlice'
import { useAppDispatch, useAppSelector } from '../../../redux/store'
import FormImmersive from '../FormImmersive'
import './bootstrap.scss'
import { FormD } from './defaults'
import styles from './index.module.scss'
import FormInputType from './inputType'
import { FormI } from './interface'
import './styles/form.scss'
import { formStartTracking, processFormSubmission } from './utils'

const Form1 = (props: FormI) => {
  const updatedProps = {
    ...FormD,
    ...props,
  }
  // debugger
  const [validation, setValidation] = useState(false)
  const [emailValidationMsg, setEmailValidationMsg] = useState<string | null>(
    ''
  )

  const formData = updatedProps?.data
  const dispatch = useAppDispatch()
  const [values, setValues] = useState({})
  // const values = useAppSelector((state) => state.form.values)

  const [formSubmitionState, setFormSubmitionState] = useState<
    'NOT SUBMITTED' | 'LOADING' | 'SUCCESS' | 'FAILED' | 'THANK YOU'
  >('NOT SUBMITTED')
  const hasValidationError = useAppSelector(
    (state) => state.form.hasValidationError
  )
  const contentfulFormId = updatedProps.data?.sys?.id
  const isFormActivated = useAppSelector((state) => state.form.isFormActivated)

  const mainHandleChange = (
    event: string,
    name: string,
    inputType?: string
  ) => {
    if (!isFormActivated[contentfulFormId]) {
      formStartTracking(contentfulFormId)
      dispatch(setIsFormActivated({ contentfulFormId }))
    }

    // Check if the inputType is provided.
    if (inputType) {
      // Check if the inputType is 'CHECKBOX'.
      if (inputType === 'CHECKBOX') {
        // Check if the values object already has the specified name.
        if (values.hasOwnProperty(name)) {
          // Check if the selected value already exists in the array of values for the given name.
          const hasvalue = values[name as keyof typeof values]?.find(
            (valueItem) => valueItem === event
          )

          // If the value exists, remove it; otherwise, add it to the array of values.
          if (hasvalue) {
            // Remove the value from the array.
            setValues({
              ...values,
              [name]: values[name].filter(
                (itemFilter: string) => itemFilter !== event
              ),
            })
          } else {
            // Add the value to the array.
            setValues({
              ...values,
              [name]: [...values[name as keyof typeof values], event],
            })
          }
        } else {
          // If the name does not exist in the values object, create a new array with the selected value.
          setValues({
            ...values,
            [name]: [event],
          })
        }
      }
      if (inputType === 'email') {
        setValues({
          ...values,
          [name]: event,
        })
      }
    } else {
      // If no inputType is provided, assume a standard input change.

      // Check if both event and name are provided.
      if (event && name) {
        // Update the values object with the new value for the specified name.
        setValues({
          ...values,
          [name]: event,
        })
      }
    }
  }

  const handleSubmitMain = useCallback(
    (values) => {
      if (hasValidationError || emailValidationMsg) return
      processFormSubmission({
        formData,
        values,
        setFormSubmitionState,
        contentfulFormId,
        hiddenFields: updatedProps.hiddenFields,
      })
    },
    [updatedProps]
  ) // Re-run the callback only when 'updatedProps' changes.

  if (updatedProps.isImmersive) {
    return <FormImmersive />
  }
  {
    /* Container for success, loading, and error toasts */
  }
  const toasts = (
    <div className={styles.btnContainer}>
      {/* Display a success toast if form submission state is "SUCCESS" */}
      {formSubmitionState === 'SUCCESS' && (
        <SuccessToast
          setToastState={() => null}
          simpleParagraph={{
            data: updatedProps?.data?.successMessage?.content,
          }}
          htmlAttr={{ className: styles.toast }}
        />
      )}

      {/* Display a loading spinner if form submission state is 'LOADING' */}
      {formSubmitionState === 'LOADING' && (
        <SpinnerToast
          setToastState={() => setFormSubmitionState('NOT SUBMITTED')}
          simpleParagraph={{
            data: updatedProps?.data?.loadingMessage?.content,
          }}
          htmlAttr={{ className: styles.toast }}
        />
      )}

      {/* Display an error toast if form submission state is 'FAILED' */}
      {formSubmitionState === 'FAILED' && (
        <ErrorToast
          setToastState={(val) => {
            !val && setFormSubmitionState('NOT SUBMITTED')
          }}
          simpleParagraph={{
            data: updatedProps?.data?.errorMessage?.content,
          }}
          htmlAttr={{ className: styles.toast }}
          onClose={() => setFormSubmitionState('NOT SUBMITTED')}
        />
      )}
    </div>
  )

  return (
    <SimpleContainer
      {...updatedProps} // Spread the properties from 'updatedProps'.
      as='div' // Render as a 'div' element.
      htmlAttr={{
        ...updatedProps.htmlAttr,
        // Combine existing class names with a custom class 'styles.formContainer'.
        className: `${props?.data?.htmlAttr?.className} ${updatedProps?.htmlAttr?.className} ${styles.formContainer}`,
        style: props?.data?.htmlAttr?.style,
      }}
    >
      {/* Check if the form submission state is "THANK YOU" */}
      {formSubmitionState === 'THANK YOU' ? (
        // Display a Richtext component with the thank-you message.
        <Richtext
          data={updatedProps?.data?.thankYouMessage}
          htmlAttr={{ className: 'mb1' }}
        />
      ) : (
        // Render a form element with certain properties and components.
        <form
          id={`form-${updatedProps?.data?.sys?.id}`}
          className={`needs-validation ${validation && 'was-validated'}`}
          // noValidate={true}
          onSubmit={(e) => {
            e.preventDefault() // Prevent the default form submission behavior.
            handleSubmitMain(values) // Call the main form submission function.
          }}
        >
          {/* Display a Richtext component for the form header if available */}
          {updatedProps?.data?.header?.content && (
            <Richtext
              data={updatedProps?.data?.header?.content}
              htmlAttr={{ style: { marginBottom: '30px' } }}
            />
          )}

          {/* Iterate through form fields and render corresponding components */}
          {formData &&
            formData?.formFieldsCollection &&
            formData?.formFieldsCollection?.items?.map(
              (input, index: number) => (
                <FormInputType
                  key={index}
                  input={input}
                  mainHandleChange={mainHandleChange}
                  values={values}
                  setValidation={setValidation}
                  formTemplate={updatedProps?.data?.template}
                  validation={validation}
                  isLightMode={!!updatedProps?.isLightMode}
                  formSubmitionState={formSubmitionState}
                  toasts={toasts}
                  contentfulFormId={contentfulFormId}
                  emailValidationMsg={emailValidationMsg}
                  setEmailValidationMsg={setEmailValidationMsg}
                  isDynamicAgreement={formData?.isDynamicAgreement}
                />
              )
            )}

          {updatedProps.hiddenFields?.map((input, index: number) => (
            <FormInputType
              key={index}
              input={input}
              primaryIndex={index}
              mainHandleChange={mainHandleChange}
              values={values}
              setValidation={setValidation}
              formSubmitionState={formSubmitionState}
              toasts={toasts}
              contentfulFormId={contentfulFormId}
            />
          ))}

          {/* Footer section */}
          {updatedProps?.data?.footer?.content && (
            <div className={styles.footerContainer}>
              {/* Display a Richtext component for the form footer if available */}

              <Richtext
                data={updatedProps?.data?.footer?.content}
                htmlAttr={{ className: styles.footer }}
              />
            </div>
          )}

          {/* Hidden fields for form data */}
          {/* Hidden field for 'sourceUrl' with an empty value */}
          {/* <input
            type='hidden'
            name='sourceUrl'
            value={''}
            className={'d-none'}
          /> */}
          {/* Hidden field for 'formId' with the Contentful ID as the value */}
          <input
            type='hidden'
            name='formid'
            value={contentfulFormId}
            className={'d-none'}
          />
        </form>
      )}
    </SimpleContainer>
  )
}

export default Form1
