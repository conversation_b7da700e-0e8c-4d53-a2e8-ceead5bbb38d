'use client'
import { AnimatePresence, motion } from 'framer-motion'
import { useCallback, useEffect, useState } from 'react'
import SimpleButtonWIcon from '../../../components/CTAs/SimpleButtonWIcon'
import SimpleContainer from '../../../components/Containers/SimpleContainer'
import Richtext from '../../../components/ContentBlocks/Richtext'
import { SimpleImage } from '../../../components/Multimedia/Images'
import { SimpleImageD } from '../../../components/Multimedia/Images/SimpleImage/defaults'
import ErrorToast from '../../../components/Notifications/Toasts/ErrorToast'
import SpinnerToast from '../../../components/Notifications/Toasts/Spinner Toast'
import { setSessionStorageItem } from '../../../globals/utils'
import {
  changeFormValueMain,
  setIsFormActivated,
} from '../../../redux/slices/formSlice'
import { setIsExitPopupShow } from '../../../redux/slices/popupSlice'
import { useAppDispatch, useAppSelector } from '../../../redux/store'
import '../Form/bootstrap.scss'
import { FormD } from '../Form/defaults'
import styles from '../Form/index.module.scss'
import { FormI } from '../Form/interface'
import '../Form/styles/form.scss'
import { formStartTracking, processFormSubmission } from '../Form/utils'
import './form.scss'
import FormInputType from './inputType'

const FormExitIntent = (props: FormI) => {
  const updatedProps = {
    ...FormD,
    ...props,
  }
  // debugger
  const [validation, setValidation] = useState(false)
  const [emailValidationMsg, setEmailValidationMsg] = useState('')

  const formData = updatedProps?.data
  const dispatch = useAppDispatch()
  const values = useAppSelector((state) => state.form.values)

  const [formSubmitionState, setFormSubmitionState] = useState<
    'NOT SUBMITTED' | 'LOADING' | 'SUCCESS' | 'FAILED' | 'THANK YOU'
  >('NOT SUBMITTED')

  const hasValidationError = useAppSelector(
    (state) => state.form.hasValidationError
  )
  const contentfulFormId = updatedProps.data?.sys?.id
  const isFormActivated = useAppSelector((state) => state.form.isFormActivated)

  useEffect(() => {
    let timerId: NodeJS.Timeout

    if (formSubmitionState === 'SUCCESS') {
      timerId = setTimeout(() => {
        dispatch(setIsExitPopupShow(false))
        setSessionStorageItem('ExitPopupDismissed', true)
        document.body.style.overflow = 'auto'
      }, 30000) // Close after 30s
    }

    return () => {
      clearTimeout(timerId)
    }
  }, [formSubmitionState])

  const mainHandleChange = (
    event: string,
    name: string,
    inputType?: string
  ) => {
    if (!isFormActivated[contentfulFormId]) {
      formStartTracking(contentfulFormId)
      dispatch(setIsFormActivated({ contentfulFormId }))
    }

    // Check if the inputType is provided.
    if (inputType) {
      if (inputType === 'email') {
        dispatch(changeFormValueMain({ [name]: event }))
      }
    } else {
      // If no inputType is provided, assume a standard input change.

      // Check if both event and name are provided.
      if (event && name) {
        // Update the values object with the new value for the specified name.
        dispatch(changeFormValueMain({ [name]: event }))
      }
    }
  }

  const handleSubmitMain = useCallback(
    (values) => {
      if (hasValidationError || emailValidationMsg) return
      processFormSubmission({
        formData,
        values,
        setFormSubmitionState,
        contentfulFormId,
        hiddenFields: updatedProps.hiddenFields,
      })
    },
    [updatedProps]
  ) // Re-run the callback only when 'updatedProps' changes.

  /* loading, and error toasts */

  const toasts = (
    <div className={'btnContainer'}>
      {/* Display a loading spinner if form submission state is 'LOADING' */}
      {formSubmitionState === 'LOADING' && (
        <SpinnerToast
          setToastState={() => setFormSubmitionState('NOT SUBMITTED')}
          simpleParagraph={{
            data: updatedProps?.data?.loadingMessage?.content,
          }}
          htmlAttr={{ className: 'toast' }}
        />
      )}

      {/* Display an error toast if form submission state is 'FAILED' */}
      {formSubmitionState === 'FAILED' && (
        <ErrorToast
          setToastState={(val) => {
            !val && setFormSubmitionState('NOT SUBMITTED')
          }}
          simpleParagraph={{
            data: updatedProps?.data?.errorMessage?.content,
          }}
          htmlAttr={{ className: 'toast' }}
          onClose={() => setFormSubmitionState('NOT SUBMITTED')}
        />
      )}
    </div>
  )

  // Animation variants
  const duration = 0.3
  const intialOpacity = 0
  const finalOpacity = 1
  const initailScale = 0.6
  const finalScale = 1

  const variants = {
    // Form animation
    initialForm: {
      opacity: finalOpacity,
      scale: finalScale,
    },
    exitForm: {
      opacity: intialOpacity,
      scale: initailScale,
      transition: { duration },
    },

    // Success animation
    initialThankYou: {
      opacity: intialOpacity,
      scale: initailScale,
    },
    animateThankYou: {
      opacity: finalOpacity,
      scale: finalScale,
      transition: { duration },
    },
    exitThankYou: {
      opacity: intialOpacity,
      scale: initailScale,
      transition: { duration },
    },
  }

  return (
    <SimpleContainer
      {...updatedProps} // Spread the properties from 'updatedProps'.
      as='div' // Render as a 'div' element.
      htmlAttr={{
        ...updatedProps.htmlAttr,
        // Combine existing class names with a custom class 'styles.formContainer'.
        className: `${props?.data?.htmlAttr?.className} ${updatedProps?.htmlAttr?.className} ${styles.formContainer} exit-form-root`,
        style: props?.data?.htmlAttr?.style,
      }}
    >
      <AnimatePresence mode='wait'>
        {/* Check if the form submission state is "THANK YOU" */}
        {formSubmitionState === 'SUCCESS' ? (
          <motion.div
            key='thankyou'
            initial='initialThankYou'
            animate='animateThankYou'
            exit='exitThankYou'
            variants={variants}
            className='contentConatiner successBg'
          >
            <SimpleImage
              {...SimpleImageD}
              {...formData?.image}
              htmlAttr={{ className: 'image' }}
            />
            <div className='successContent'>
              {/*  Display a Richtext component with the thank-you message. */}
              <Richtext
                htmlAttr={{ className: 'heading' }}
                data={updatedProps?.data?.thankYouMessage}
              />
              <SimpleButtonWIcon
                textContent='Done'
                variant='primary'
                isIconPrefixed
                icon='Tickmark'
                isFormButton
                isLightMode={false}
                htmlAttr={{
                  className: 'ok-cta',
                  onClick: () => {
                    dispatch(setIsExitPopupShow(false))
                    setSessionStorageItem('ExitPopupDismissed', true)
                    document.body.style.overflow = 'auto'
                  },
                }}
              />
            </div>
          </motion.div>
        ) : (
          <motion.div
            key='form'
            initial='initialForm'
            animate='initialForm'
            exit='exitForm'
            variants={variants}
            className='contentConatiner initailBg'
            layout
          >
            <SimpleImage
              {...SimpleImageD}
              {...formData?.image}
              htmlAttr={{ className: 'image' }}
            />
            {/*         Render a form element with certain properties and components.
             */}{' '}
            <form
              id={`form-${updatedProps?.data?.sys?.id}`}
              className={`needs-validation ${validation && 'was-validated'} initailContent`}
              // noValidate={true}
              onSubmit={(e) => {
                e.preventDefault() // Prevent the default form submission behavior.
                handleSubmitMain(values) // Call the main form submission function.
              }}
            >
              {/* Display a Richtext component for the form header if available */}
              {updatedProps?.data?.header?.content && (
                <Richtext
                  data={updatedProps?.data?.header?.content}
                  htmlAttr={{ className: 'heading' }}
                />
              )}
              {formSubmitionState !== 'NOT SUBMITTED' ? (
                toasts
              ) : (
                <div className='exit-form-content'>
                  {/* Iterate through form fields and render corresponding components */}
                  {formData &&
                    formData?.formFieldsCollection &&
                    formData?.formFieldsCollection?.items?.map(
                      (input, index: number) => (
                        <FormInputType
                          key={index}
                          input={input}
                          primaryIndex={index}
                          mainHandleChange={mainHandleChange}
                          values={values}
                          setValidation={setValidation}
                          formSubmitionState={formSubmitionState}
                          toasts={toasts}
                          contentfulFormId={contentfulFormId}
                          emailValidationMsg={emailValidationMsg}
                          setEmailValidationMsg={setEmailValidationMsg}
                          isDynamicAgreement={formData?.isDynamicAgreement}
                        />
                      )
                    )}
                </div>
              )}
            </form>
          </motion.div>
        )}
      </AnimatePresence>
    </SimpleContainer>
  )
}

export default FormExitIntent
