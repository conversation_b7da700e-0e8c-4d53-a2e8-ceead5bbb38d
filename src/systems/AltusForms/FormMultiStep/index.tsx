'use client'
// import Button from 'components/Button'
// import { AltusRichText } from 'components/RichText'
// import CloseButton from 'components/ShareIcons/@core/Close/closeButton'
import { useCallback, useEffect, useRef, useState } from 'react'
import SimpleContainer from '../../../components/Containers/SimpleContainer'
// import Store from 'redux/store'
// import {getPageLocationInfo, isLangEn, log, sessionGetItem} from 'utils'
import Richtext from '../../../components/ContentBlocks/Richtext'
// import { closeForm, handleAllChange, handleAllReset, handleAllSubmit, setEndpointUrl } from './events'
import { motion } from 'framer-motion'
import SimpleButtonWIcon from '../../../components/CTAs/SimpleButtonWIcon'
import ErrorToast from '../../../components/Notifications/Toasts/ErrorToast'
import SpinnerToast from '../../../components/Notifications/Toasts/Spinner Toast'
import SuccessToast from '../../../components/Notifications/Toasts/SuccessToast'
import { useTranslation } from '../../../globals/utils'
import { useWindowSize } from '../../../hooks/useWindowSize'
import {
  setIsFormActivated
} from '../../../redux/slices/formSlice'
import { useAppDispatch, useAppSelector } from '../../../redux/store'
import { FormD } from '../Form/defaults'
import styles from '../Form/index.module.scss'
import FormInputType from '../Form/inputType'
import { FormI } from '../Form/interface'
import '../Form/styles/form.scss'
import { formStartTracking, processFormSubmission } from '../Form/utils'
import FormImmersive from '../FormImmersive'

const MultiStep = (props: FormI) => {
  const updatedProps = {
    ...FormD,
    ...props,
  }
  const { width } = useWindowSize()
  const [formData, setFormData] = useState(updatedProps?.data)
  const [toValidate, setToValidate] = useState<null | number>(null)
  const [currentStep, setCurrentStep] = useState(0)
  const [containerHeight, setContainerHeight] = useState<number | 'auto'>(
    'auto'
  )
  const [emailValidationMsg, setEmailValidationMsg] = useState<string | null>(
    null
  )

  const sectionRefs = useRef<(HTMLFormElement | null)[]>([]) //refs array for all form sections

  const hasValidationError = useAppSelector(
    (state) => state.form.hasValidationError
  )
  const [formSubmitionState, setFormSubmitionState] = useState<
    'NOT SUBMITTED' | 'LOADING' | 'SUCCESS' | 'FAILED' | 'THANK YOU'
  >('NOT SUBMITTED')
  const [formSubmitionStateErrorMsg, setFormSubmitionStateErrorMsg] =
    useState('')
  const contentfulFormId = updatedProps.data?.sys?.id

  if (updatedProps.isImmersive) {
    return <FormImmersive />
  }

  const dispatch = useAppDispatch()
  const [values, setValues] = useState({})
  // const values = useAppSelector((state) => state.form.values)
  const isFormActivated = useAppSelector((state) => state.form.isFormActivated)

  const mainHandleChange = (
    event: string,
    name: string,
    inputType?: string
  ) => {
    if (!isFormActivated[contentfulFormId]) {
      formStartTracking(contentfulFormId)
      dispatch(setIsFormActivated({ contentfulFormId }))
    }

    if (inputType) {
      if (inputType === 'CHECKBOX') {
        if (values.hasOwnProperty(name)) {
          const hasvalue = values[name as keyof typeof values]?.find(
            (valueItem) => valueItem === event
          )
          if (hasvalue) {
            setValues({
              ...values,
              [name]: values[name].filter(
                (itemFilter: string) => itemFilter !== event
              ),
            })
          } else {
            setValues({
              ...values,
              [name]: [...values[name as keyof typeof values], event],
            })
          }
        } else {
          setValues({
            ...values,
            [name]: [event],
          })
        }
      }
      if (inputType === 'email') {
        if (event && name) {
          setValues({
            ...values,
            [name]: event,
          })
        }
      }
    } else {
      if (event && name) {
        setValues({
          ...values,
          [name]: event,
        })
      }
    }
  }

  const formFields = formData?.formFieldsCollection?.items || []

  let section = formFields.filter(
    (e) =>
      e?.hasOwnProperty('sectionItemsCollection') &&
      e?.sectionItemsCollection.hasOwnProperty('items') &&
      e?.sectionItemsCollection.items?.length > 0
  )

  const handleSubmitMain = useCallback(
    (values) => {
      if (emailValidationMsg) {
        return
      }
      processFormSubmission({
        formData,
        values,
        setFormSubmitionStateErrorMsg,
        setFormSubmitionState,
        contentfulFormId,
        hiddenFields: updatedProps.hiddenFields,
        section,
      })
    },
    [updatedProps, section]
  )
  const adjustHeight = () => {
    if (!isNaN(currentStep) && sectionRefs?.current?.[currentStep]) {
      const activeForm = sectionRefs?.current?.[currentStep]
      if (activeForm) {
        const { height } = activeForm.getBoundingClientRect()
        setContainerHeight(height)
      }
    }
  }

  const nextStep = () => {
    if (hasValidationError) return
    setCurrentStep((prevStep) => prevStep + 1)
  }

  const prevStep = () => {
    setCurrentStep((prevStep) => Math.max(prevStep - 1, 0))
  }

  useEffect(() => {
    adjustHeight()
  }, [currentStep, mainHandleChange, width])

  // let button = formFields.filter(
  //   (e) => e?.hasOwnProperty('__typename') && e?.__typename === 'LinkComponent'
  // )

  const carouselClass = (currentStep: number, index: number) => {
    if (currentStep === index) return 'form-active'
    if (currentStep > index) return 'form-left'
    if (currentStep < index) return 'form-right'
    return ''
  }

  const formHeader = updatedProps.data?.header?.content && (
    <Richtext
      data={updatedProps.data?.header?.content}
      htmlAttr={{ style: { marginBottom: '30px' } }}
    />
  )

  const formFooter = updatedProps.data?.footer?.content && (
    <Richtext
      data={updatedProps.data?.footer?.content}
      htmlAttr={{ style: { paddingBottom: '30px' } }}
    />
  )

  let TheFormSections = section.map((field, index: number) => (
    <form
      ref={(el) => {
        if (el && sectionRefs?.current) {
          sectionRefs.current[index] = el
        }
      }}
      id={`form-${contentfulFormId}-section-${index}`}
      onSubmit={(e) => {
        e.preventDefault()
        if (!hasValidationError && !emailValidationMsg) {
          if (currentStep + 1 === section.length) {
            handleSubmitMain(values)
          } else {
            nextStep()
          }
        }
      }}
      key={`form-${contentfulFormId}-section-${index}`}
      className={
        toValidate === index
          ? `was-validated ${carouselClass(currentStep, index)}`
          : `${carouselClass(currentStep, index)}`
      }
    >
      {/* Header */}
      {/* {field?.header?.content ? (
        <Richtext
          data={field?.header?.content}
          htmlAttr={{ style: { marginBottom: '30px', paddingRight: '10px' } }}
        />
      ) : index === 0 && <>{formHeader}</>} */}
      {/* Global header */}
      {index === 0 && <>{formHeader}</>}

      {/* Inputs Sections */}
      <FormInputType
        input={field}
        primaryIndex={index}
        mainHandleChange={mainHandleChange}
        values={values}
        formTemplate={updatedProps?.data?.template}
        emailValidationMsg={emailValidationMsg}
        setEmailValidationMsg={setEmailValidationMsg}
        isDynamicAgreement={formData?.isDynamicAgreement}
      />
      {/* Next and Previous button container */}

      {index === section?.length - 1 &&
        formSubmitionState !== 'NOT SUBMITTED' ? (
        <div className={styles.btnContainer}>
          {formSubmitionState === 'SUCCESS' && (
            <SuccessToast
              simpleParagraph={{
                data: updatedProps?.data?.successMessage?.content,
              }}
              setToastState={() => null}
              htmlAttr={{ className: styles.toast }}
            />
          )}
          {/*  Loading Message  */}
          {formSubmitionState === 'LOADING' && (
            <SpinnerToast
              simpleParagraph={{
                data: updatedProps?.data?.loadingMessage?.content,
              }}
              setToastState={() => setFormSubmitionState('NOT SUBMITTED')}
              htmlAttr={{ className: styles.toast }}
            />
          )}
          {/*  Error Message  */}
          {formSubmitionState === 'FAILED' && (
            <ErrorToast
              setToastState={(val) => {
                !val && setFormSubmitionState('NOT SUBMITTED')
              }}
              simpleParagraph={{
                data: formSubmitionStateErrorMsg
                  ? formSubmitionStateErrorMsg
                  : updatedProps?.data?.errorMessage?.content,
              }}
              onClose={() => setFormSubmitionState('NOT SUBMITTED')}
              htmlAttr={{ className: styles.toast }}
            />
          )}
        </div>
      ) : (
        <div className={styles.multistepButtons}>
          {/* Previous button */}

          <div
            className={`${styles.preButton} mt1 ${currentStep === 0 && styles.hidden
              }`}
            onClick={prevStep}
          >
            {useTranslation('Go back', updatedProps?.data?.locale)}
          </div>
          {/* Next button */}
          <SimpleButtonWIcon
            isFormButton
            isButton
            variant='primary'
            textContent={`${currentStep === section?.length - 1
              ? useTranslation('Submit', updatedProps?.data?.locale)
              : useTranslation('Continue', updatedProps?.data?.locale)
              }`}
            isChevron2Arrow
            htmlAttr={{
              type: 'submit',
              onClick: () => setToValidate(index),
              className: `${styles.nextButton} mt1`,
            }}
          />
        </div>
      )}
      {index === 0 &&
        updatedProps?.hiddenFields?.map((field, ind) => (
          <FormInputType key={ind} input={field} values={values} />
        ))}

      {/* Section Footer */}
      {/* {field?.footer?.content && (
        <Richtext
          data={field?.footer?.content}
          htmlAttr={{ style: { marginBottom: '30px' } }}
        />
      )} */}

      {/*Global Footer */}
      {index === section?.length - 1 && <>{formFooter}</>}
    </form>
  ))

  return (
    <SimpleContainer
      {...updatedProps}
      as='div'
      htmlAttr={{
        ...updatedProps?.htmlAttr,
        className: `${props?.data?.htmlAttr?.className} ${updatedProps?.htmlAttr?.className} ${styles.formContainer}`,
        style: {
          overflow: 'hidden',
        },
      }}
    >
      {formSubmitionState === 'THANK YOU' ? (
        <Richtext
          data={updatedProps?.data?.thankYouMessage}
          htmlAttr={{ className: 'mb1' }}
        />
      ) : (
        <div role='form' className='formDiv' style={{ minHeight: 'auto' }}>
          {/* {formHeader} */}
          {/* Dynamic Form component to generate the form */}
          <motion.div
            animate={{ height: containerHeight }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
          >
            {TheFormSections}
          </motion.div>
          <input
            type='hidden'
            name='formid'
            value={contentfulFormId}
            className={'d-none'}
          />
        </div>
      )}
    </SimpleContainer>
  )
}

export default MultiStep
