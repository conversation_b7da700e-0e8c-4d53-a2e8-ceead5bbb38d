'use client'
import { useCallback, useEffect, useRef, useState } from 'react'
import LayoutContainer from '../../../components/Containers/LayoutContainer'
import Richtext from '../../../components/ContentBlocks/Richtext'
import Kernel from '../../../components/Kernel'
import GenericIcon from '../../../components/Multimedia/Icons/SysIcon'
import { BackgroundImage } from '../../../components/Multimedia/Images'
import ErrorToast from '../../../components/Notifications/Toasts/ErrorToast'
import InfoToast from '../../../components/Notifications/Toasts/InfoToast'
import SpinnerToast from '../../../components/Notifications/Toasts/Spinner Toast'
import SuccessToast from '../../../components/Notifications/Toasts/SuccessToast'
import {
  getLocalStorageItem,
  getSessionStorageItem,
  setSessionStorageItem,
} from '../../../globals/utils'
import {
  changeFormValueMain,
  setIsFormActivated,
} from '../../../redux/slices/formSlice'
import { useAppDispatch, useAppSelector } from '../../../redux/store'
import FormInputType from '../Form/inputType'
import {
  GA4FormCloseTracking,
  formStartTracking,
  processFormSubmission,
} from '../Form/utils'
import { FormFloatingD } from './defaults'
import styles from './index.module.scss'
import { FormFloatingI } from './interface'

export default function FormFloating(props: FormFloatingI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */

  const updatedProps: FormFloatingI = {
    ...FormFloatingD,
    ...props,
  }
  const newsletterRef = useRef<HTMLDivElement | null>(null)
  const contentfulFormId = updatedProps.data?.sys?.id

  const isSubscribed = Boolean(
    getSessionStorageItem('altusNewsLetterSubscribed')
  )

  const FormValues = getLocalStorageItem(contentfulFormId)

  const colorClass = updatedProps?.data?.isLightMode ? 'cp1' : 'cs2'

  const [shouldRenderForm, setShouldRenderForm] = useState(false)
  const [isFormRendered, setIsFormRendered] = useState(false)
  const [removeForm, setRemoveForm] = useState(false)
  const [initialAnimate, setInitialAnimate] = useState(true)
  const [formData, setFormData] = useState(updatedProps?.data)
  const [validation, setValidation] = useState(false)
  const [values, setValues] = useState({ ...FormValues })
  const [emailValidationMsg, setEmailValidationMsg] = useState<string | null>(
    null
  )
  const [formFiledValues, setFormFieldValue] = useState<Boolean>(
    Boolean(FormValues) || false
  )

  const isDisplayPostAction = updatedProps?.data?.displayPostAction

  const isStatic = updatedProps?.data?.isStatic
  const dispatch = useAppDispatch()
  const isFormActivated = useAppSelector((state) => state.form.isFormActivated)

  const emailErrorMsg = useAppSelector(
    (state) => state.form.floatingFormEmailError
  )

  const [formSubmitionState, setFormSubmitionState] = useState<
    'NOT SUBMITTED' | 'LOADING' | 'SUCCESS' | 'FAILED' | 'THANK YOU'
  >('NOT SUBMITTED')

  // function to handle form input
  const mainHandleChange = (event: string, name: string) => {
    if (!isFormActivated[contentfulFormId]) {
      formStartTracking(contentfulFormId)
      dispatch(setIsFormActivated({ contentfulFormId }))
    }

    if (event && name) {
      setValues((prev) => ({
        ...prev,
        [name]: event,
      }))
    }
  }

  const storeAltusNewsLetterFlagToSession = () =>
    setSessionStorageItem('altusNewsLetterSubscribed', true)

  useEffect(() => {
    if (formSubmitionState === 'SUCCESS' && !isStatic && !isDisplayPostAction) {
      setTimeout(() => {
        storeAltusNewsLetterFlagToSession()
        setFormFieldValue(true)
        setRemoveForm(true)
      }, 2000)
    }
  }, [formSubmitionState])

  // function to handle form submission
  const handleSubmitMain = useCallback(
    (values) => {
      if (emailErrorMsg) return
      processFormSubmission({
        formData: { ...formData, setToLocalStorage: isStatic ? false : true },
        values,
        setFormSubmitionState,
        contentfulFormId,
        hiddenFields: updatedProps.hiddenFields,
      })
    },
    [updatedProps]
  )

  useEffect(() => {
    if (isStatic) return
    // Check localStorage on mount
    const storedValues = localStorage.getItem(contentfulFormId)
    const parsedValues = storedValues ? JSON.parse(storedValues) : null

    if (parsedValues) {
      setFormFieldValue(Boolean(parsedValues))
      dispatch(changeFormValueMain({ ...parsedValues }))
    }

    // Listen for changes to localStorage
    const handleStorageChange = (event: Event) => {
      const values = localStorage.getItem(contentfulFormId)
      const updatedValues = values ? JSON.parse(values) : null
      if (updatedValues) {
        setFormFieldValue(Boolean(updatedValues))
        dispatch(changeFormValueMain({ ...updatedValues }))
      }
    }

    window.addEventListener('localStorageChange', handleStorageChange)

    return () => {
      window.removeEventListener('localStorageChange', handleStorageChange)
    }
  }, [])

  // useEffect to render the form when 45% of the page is scrolled
  useEffect(() => {
    const handleScroll = () => {
      const scrollPercentage =
        (window.scrollY /
          (document.documentElement.scrollHeight - window.innerHeight)) *
        100

      if (!isFormRendered) {
        const displayAt = updatedProps?.data?.displayAt
          ? Number(updatedProps?.data?.displayAt)
          : 50
        if (scrollPercentage > displayAt) {
          setShouldRenderForm(true)
          setIsFormRendered(true)
          setTimeout(() => {
            setInitialAnimate(false)
          }, 500)
        }
      }
    }

    if (isStatic) {
      setShouldRenderForm(true)
      setIsFormRendered(true)
    } else {
      window.addEventListener('scroll', handleScroll)

      return () => {
        window.removeEventListener('scroll', handleScroll)
      }
    }
  }, [isFormRendered])

  useEffect(() => {
    if (isStatic) return

    const fixFooterScroll = () => {
      if (document) {
        const footer = document?.getElementById('footer')
        const footerRect = footer?.getBoundingClientRect()
        const newsletterFlootingAltus = newsletterRef?.current

        if (!footer || !newsletterFlootingAltus) return

        const distanceFromFooter = window.innerHeight - footerRect?.top

        if (distanceFromFooter > 0) {
          if (newsletterFlootingAltus)
            newsletterFlootingAltus.style.bottom = `${distanceFromFooter}px`
        } else {
          if (newsletterFlootingAltus)
            newsletterFlootingAltus.style.bottom = '0px'
        }
      }
    }

    window.addEventListener('scroll', fixFooterScroll)

    return () => {
      window.removeEventListener('scroll', fixFooterScroll)
    }
  }, [])

  useEffect(() => {
    if (isDisplayPostAction && formSubmitionState === 'SUCCESS') {
      const toastContainer = document.getElementById('infoToastRichtextId')

      const anchorTag = toastContainer?.querySelector('a')

      if (anchorTag) {
        anchorTag.onclick = () => {
          storeAltusNewsLetterFlagToSession()
          setRemoveForm(true)
        }
      }
    }
  }, [isDisplayPostAction, formSubmitionState])

  const toasts = (
    <div className={styles.toasts}>
      {formSubmitionState === 'SUCCESS' && (
        <>
          {!isDisplayPostAction ? (
            <SuccessToast
              simpleParagraph={{
                data: updatedProps?.data?.successMessage?.content,
              }}
              onClose={() => setFormSubmitionState('NOT SUBMITTED')}
              autoHide={!isStatic}
              htmlAttr={{
                style: { width: '100%', maxWidth: '900px' },
              }}
            />
          ) : (
            <InfoToast
              simpleParagraph={{
                data: updatedProps?.data?.postSuccessMessage?.content,
              }}
              onClose={() => setFormSubmitionState('NOT SUBMITTED')}
              autoHide={false}
              htmlAttr={{
                style: { width: '100%', maxWidth: '900px' },
              }}
              isPersistent={true}
            />
          )}
        </>
      )}
      {formSubmitionState === 'LOADING' && (
        <SpinnerToast
          onClose={() => setFormSubmitionState('NOT SUBMITTED')}
          simpleParagraph={{
            data: updatedProps?.data?.loadingMessage?.content,
          }}
          htmlAttr={{
            style: { width: '100%', maxWidth: '900px' },
          }}
        />
      )}
      {formSubmitionState === 'FAILED' && (
        <ErrorToast
          simpleParagraph={{
            data: updatedProps?.data?.errorMessage?.content,
          }}
          autoHide={!isStatic}
          onClose={() => setFormSubmitionState('NOT SUBMITTED')}
          htmlAttr={{
            style: { width: '100%', maxWidth: '900px' },
          }}
        />
      )}
    </div>
  )

  const BgImage = updatedProps?.data?.backgroundImage ? (
    <>
      <BackgroundImage
        sizes={'100%'}
        isFullXBleed
        zIndex={1}
        src={updatedProps?.data?.backgroundImage?.imageFile?.url}
      />
      <div className={'imageOverlay bo1n1d'}></div>
    </>
  ) : (
    <></>
  )

  function shouldDisplayContent() {
    const isDynamicContent = !isStatic
    const formNotNeededOrSubmitted =
      !shouldRenderForm || removeForm || formSubmitionState === 'THANK YOU'

    return isSubscribed || (isDynamicContent && formNotNeededOrSubmitted)
  }

  return shouldDisplayContent() ? (
    <></>
  ) : (
    <Kernel
      {...updatedProps}
      isFullXBleed
      htmlAttr={{
        className: `${styles.formFloatingRoot} ${isStatic ? ' ' : initialAnimate && styles.initialAnimate
          } ${isStatic ? ' ' : !shouldRenderForm && styles.initialState} ${updatedProps?.data?.htmlAttr?.className
          }`,
        style: {
          position: `${isStatic ? 'relative' : 'fixed'}`,
          zIndex: `${isStatic ? 1 : 10}`,
        },
        ref: newsletterRef,
      }}
    >
      {BgImage}
      <LayoutContainer htmlAttr={{ style: { height: '100%' } }}>
        {!isStatic && (
          <GenericIcon
            icon='Close'
            htmlAttr={{
              className: `${styles.closeButton} ${colorClass}`,
              onClick: () => {
                setRemoveForm(true)
                if (formFiledValues) {
                  storeAltusNewsLetterFlagToSession()
                }
                GA4FormCloseTracking(contentfulFormId)
              },
            }}
            isLightMode={updatedProps?.data?.isLightMode}
          />
        )}
        <div className={styles.blurContainer}>
          <div className={styles.formFloatingContainer}>
            {formSubmitionState === 'THANK YOU' && !isDisplayPostAction ? (
              <Richtext data={updatedProps?.data?.thankYouMessage} />
            ) : (
              <form
                id={`form-${contentfulFormId}`}
                onSubmit={(e) => {
                  e.preventDefault()
                  handleSubmitMain(values)
                }}
                className={styles.fromCon}
              >
                <div className={styles.contentContainer}>
                  {updatedProps?.data?.header?.content && (
                    <div className={styles.leftSection}>
                      <Richtext
                        htmlAttr={{ className: `${colorClass}` }}
                        isLightMode={updatedProps?.data?.isLightMode}
                        data={updatedProps?.data?.header?.content}
                      />
                    </div>
                  )}
                  {formSubmitionState !== 'NOT SUBMITTED' ? (
                    toasts
                  ) : (
                    <div className={styles.formFloatingInputDiv}>
                      {formSubmitionState === 'NOT SUBMITTED' &&
                        formData &&
                        formData?.formFieldsCollection &&
                        formData?.formFieldsCollection?.items?.map(
                          (input, index: number) => (
                            <FormInputType
                              key={index}
                              input={input}
                              contentfulFormId={contentfulFormId}
                              formSubmitionState={formSubmitionState}
                              toasts={toasts}
                              formTemplate={formData?.template}
                              setValidation={setValidation}
                              validation={validation}
                              mainHandleChange={mainHandleChange}
                              values={values}
                              isLightMode={updatedProps?.data?.isLightMode}
                              setEmailValidationMsg={setEmailValidationMsg}
                              emailValidationMsg={emailValidationMsg}
                              isDynamicAgreement={formData?.isDynamicAgreement}
                            />
                          )
                        )}
                    </div>
                  )}
                </div>
                {updatedProps.hiddenFields?.map((input, index: number) => (
                  <FormInputType
                    key={index}
                    input={input}
                    primaryIndex={index}
                    mainHandleChange={mainHandleChange}
                    values={values}
                    formSubmitionState={formSubmitionState}
                    toasts={toasts}
                    contentfulFormId={contentfulFormId}
                  />
                ))}
                <input
                  type='hidden'
                  name='formid'
                  value={contentfulFormId}
                  className={'d-none'}
                />
              </form>
            )}
          </div>
        </div>
      </LayoutContainer>
    </Kernel>
  )
}
