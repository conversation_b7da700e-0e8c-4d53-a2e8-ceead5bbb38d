import { geolocation } from '@vercel/functions'
import { NextRequest, NextResponse } from 'next/server'
import { CONTINENT_REGION_MAP } from '../../../globals/utils'

export async function GET(request: NextRequest) {
    const continent: string = request?.headers?.get('x-vercel-ip-continent') || ''
    const country: string = request?.headers?.get('x-vercel-ip-country') || ''
    const city: string = request?.headers?.get('x-vercel-ip-city') || ''
    const geo = geolocation(request)
    const region = CONTINENT_REGION_MAP[continent] || 'AMER' // default to AMER if unmapped

    return NextResponse.json({ continent, country, city, geo, region })
}
