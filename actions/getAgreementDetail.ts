'use server'

import { getDomainShortName } from "../src/globals/utils"
import { getConfigurationByScopeAndType } from "../src/lib/experimentation"

/**
 * Get fileId from Contentful Assets configuration by MCA URL
 * @param mcaUrl - The MCA URL (e.g., MCA_LINKS.USA)
 * @returns Promise<string | null> - The fileId if found, null otherwise
 */
export async function getAgreementDetail(mcaUrl: string): Promise<string | null> {
    try {
        const domainShortName = getDomainShortName(process.env.NEXT_PUBLIC_DOMAIN || '')
        if (!domainShortName) {
            console.error('Unable to determine domain short name')
            return null
        }

        const assetsData = await getConfigurationByScopeAndType(
            'Assets',
            domainShortName.toUpperCase()
        )

        if (!assetsData || !Array.isArray(assetsData)) {
            console.error('No assets configuration found or invalid format')
            return null
        }

        // Extract the path from the MCA URL to match against the source field
        let urlPath = ''
        try {
            const url = new URL(mcaUrl)
            urlPath = url.pathname
        } catch {
            // If it's not a full URL, treat it as a path
            urlPath = mcaUrl.startsWith('/') ? mcaUrl : `/${mcaUrl}`
        }

        // Remove .pdf extension from urlPath if it exists
        const normalizedUrlPath = urlPath.replace(/\.pdf$/, '')

        // Find the asset that matches the MCA URL
        const matchingAsset = assetsData.find((asset: any) => {
            // Normalize asset source by removing .pdf if it exists
            const normalizedSource = (asset?.source || '').replace(/\.pdf$/, '')

            // Primary match: Check if the normalized asset source matches the normalized URL path
            if (normalizedSource === normalizedUrlPath) {
                return true
            }
            return false
        })

        if (matchingAsset) {
            // Return fileId first
            return matchingAsset.fileId || null
        }

        console.warn(`No matching asset found for MCA URL: ${mcaUrl}`)
        console.warn(`Searched for path: ${urlPath}`)
        return null
    } catch (error) {
        console.error('Error fetching fileId by MCA URL:', error)
        return null
    }
}
