interface PageCollectionItem {
  slug?: string
  experimentationId?: string
  experimentationType?: string
  experimentationPages: string[]
}

interface GraphQLResponse {
  data?: {
    pageCollection?: {
      items?: PageCollectionItem[]
    }
  }
}
interface PathData {
  experimentationId: string
  experimentationType?: string
  experimentationPages?: string[]
  historyCount?: number
}

interface CookieSetTopagesforExperimentationParams {
  req: NextRequest
  response?: NextResponse
  userId?: string
  hasUserId?: boolean
  EvenOddGroup?: string
  hasUserEvenOddId?: boolean
  UserEvenOddId?: string | number
  origin?: string
}

interface EmailFieldProps {
  input: {
    altusFieldName: string
    altusLabel: string
    altusIsRequired: boolean
    placeholderText: string
    validationErrorMessage: string
    isAltusEmailAllowed: boolean
    isGenericEmailAllowed: boolean
    helperText: string
  }
  formTemplate: string
  setEmailValidationMsg: Dispatch<SetStateAction<string | null>>
  emailValidationMsg: string | null
  mainHandleChange: (event: string, name: string, inputType?: string) => void
  values: Record<string, any>
  isLightMode?: boolean
  validation: boolean
}

interface IEmailValidationMsg {
  inputValue: string
  validationErrorMessage: string
  isAltusEmailAllowed: boolean
  isGenericEmailAllowed: boolean
  formTemplate: string
}

interface FormInputTypeProps {
  input: any
  mainHandleChange: (event: string, name: string) => void
  values: Record<string, any>
  setValidation: (state: boolean) => void
  formTemplate: string
  formSubmitionState:
  | 'NOT SUBMITTED'
  | 'LOADING'
  | 'SUCCESS'
  | 'FAILED'
  | 'THANK YOU'
  toasts: JSX.Element
  contentfulFormId: string
  setEmailValidationMsg: (msg: string | null) => void
  emailValidationMsg: string | null
  isLightMode: boolean
  validation: boolean
  isDynamicAgreement?: boolean
}

interface ExperimentationPageData {
  data?: {
    experimentationId: string
    experimentationType?: string
    masterPage?: string
    isPublished?: boolean
    isDeleted?: boolean
    historyCount?: number
    experimentationPages?: {
      slug?: string
    }[]
  }[]
}
