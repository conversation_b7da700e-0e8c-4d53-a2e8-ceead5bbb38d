{"name": "altusgroup", "version": "3.0.0", "private": true, "scripts": {"dev": "next dev", "build": "yarn prebuild && next build && node src/algolia/algolia-sync.js", "prebuild": "yarn move-files && node automation/updateRevalidateTime.cjs", "start": "next start", "lint": "next lint", "format": "prettier --config ./.prettierrc --write 'src/**/*.{js,jsx,ts,tsx,json,css,scss,md,html}'", "createComponent": "node automation/createComponentFiles", "check-commit": "node preCommitCheck", "postinstall": "husky install", "createSkeleton": "node automation/basicSkeleton", "create-family": "node automation/createFamily.js", "create-familycomp": "node automation/createFamilyComp.js", "create-genus": "node automation/createGenus.js", "create-genuscomp": "node automation/createGenusComp.js", "move-robots": "node automation/moveRobotsTxt.cjs", "move-files": "node automation/moveFiles.cjs"}, "dependencies": {"@algolia/recommend": "^4.20.0", "@algolia/recommend-react": "^1.10.0", "@google-cloud/translate": "^8.0.2", "@next/env": "^13.4.19", "@react-google-maps/api": "^2.19.2", "@reduxjs/toolkit": "^1.9.6", "@statsig/client-core": "^1.4.0", "@statsig/js-client": "^1.4.0", "@statsig/react-bindings": "^1.4.0", "@types/canvas-confetti": "^1.9.0", "@types/papaparse": "^5.3.9", "@types/react-dom": "18.2.6", "@types/topojson-client": "^3.1.4", "@typescript-eslint/parser": "^6.7.4", "@vercel/functions": "^2.1.0", "@vercel/speed-insights": "^1.0.11", "@wokaylabs/tiptap-react-render": "^0.0.1", "algoliasearch": "^4.20.0", "antd": "^5.24.0", "canvas-confetti": "^1.9.3", "csv-parser": "^3.0.0", "csvtojson": "^2.0.10", "d3-geo": "^3.1.1", "dotenv": "^16.3.1", "echarts": "^5.5.0", "echarts-for-react": "^3.0.2", "firebase": "^11.5.0", "firebase-admin": "^12.2.0", "framer-motion": "^11.11.17", "jspdf": "^2.5.1", "moment": "^2.29.4", "next": "14.2.3", "papaparse": "^5.4.1", "react": "latest", "react-bootstrap-icons": "^1.10.2", "react-dom": "latest", "react-error-boundary": "^4.0.13", "react-icons": "^4.10.1", "react-instantsearch": "^7.3.0", "react-redux": "^9.1.2", "react-share": "^5.0.3", "react-tooltip": "^5.21.4", "sass": "^1.64.2", "statsig-node": "^5.23.1", "statsig-react": "^2.1.0", "topojson-client": "^3.1.0", "xlsx": "^0.18.5", "yt-player": "^3.6.1"}, "type": "module", "devDependencies": {"@rollup/plugin-commonjs": "^25.0.4", "@rollup/plugin-node-resolve": "^15.2.0", "@rollup/plugin-typescript": "^11.1.2", "@types/d3-geo": "^3.1.0", "@types/node": "20.3.2", "@types/react": "18.2.14", "@types/yt-player": "^3.5.1", "@typescript-eslint/eslint-plugin": "^6.4.1", "eslint": "^8.47.0", "eslint-plugin-storybook": "^0.6.14", "eslint-plugin-unused-imports": "^3.0.0", "fs-extra": "^11.1.1", "husky": "^8.0.0", "inquirer": "^9.2.10", "node-sass": "^9.0.0", "postcss-import": "^15.1.0", "prettier": "^3.3.3", "rollup": "^3.29.0", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-styles": "^4.0.0", "styled-components": "^6.0.0-rc.1", "stylelint": "^15.10.3", "stylelint-config-standard": "^34.0.0", "typescript": "5.1.6"}, "packageManager": "yarn@1.22.0"}